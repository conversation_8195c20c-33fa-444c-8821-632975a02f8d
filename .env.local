# Local Development Environment Variables
# Copy this file and rename to .env.local for local development

# Supabase Configuration
# Note: Fixed the URL - should be API URL, not dashboard URL
SUPABASE_URL=https://uwizdypmlvfvegklnogq.supabase.co
NEXT_PUBLIC_SUPABASE_URL=https://uwizdypmlvfvegklnogq.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV3aXpkeXBtbHZmdmVna2xub2dxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5Mzk3MDUsImV4cCI6MjA2NjUxNTcwNX0.KQ3_XiXJERD7mA4iZOaon82hcR7g6PBigelW_rie7Ew

# Go to: Project Settings > API > service_role key (secret)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InV3aXpkeXBtbHZmdmVna2xub2dxIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDkzOTcwNSwiZXhwIjoyMDY2NTE1NzA1fQ.9uRj8ao4jKsCOvmcnDYso5vP6CZHuUW2w0irGLDE9LQ

# Application Settings
JWT_SECRET=cknBU1dk+r6v/xZojqR7fHOhDpBNltk6offArhevQpG7YS6AVio3n2iRD/ASCjAmSa/y9Rx1Z00IUxxvHUu58g==

# Database Connection (for direct database access if needed)
DATABASE_URL=postgresql://postgres.uwizdypmlvfvegklnogq:<EMAIL>:5432/postgres

# Development Mode
NODE_ENV=development
